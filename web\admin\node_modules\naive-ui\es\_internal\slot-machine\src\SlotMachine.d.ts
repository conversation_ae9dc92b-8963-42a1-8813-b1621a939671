declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    value: {
        type: (StringConstructor | NumberConstructor)[];
        default: number;
    };
    max: {
        type: NumberConstructor;
        default: undefined;
    };
    appeared: {
        type: BooleanConstructor;
        required: true;
    };
}>, () => JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    value: {
        type: (StringConstructor | NumberConstructor)[];
        default: number;
    };
    max: {
        type: NumberConstructor;
        default: undefined;
    };
    appeared: {
        type: BooleanConstructor;
        required: true;
    };
}>> & Readonly<{}>, {
    value: string | number;
    max: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
