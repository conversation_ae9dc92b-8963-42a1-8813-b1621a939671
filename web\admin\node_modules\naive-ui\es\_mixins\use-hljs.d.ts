import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'highlight.js';
import { type ComputedRef, type Ref } from 'vue';
interface UseHljsProps {
    hljs?: unknown;
    [key: string]: unknown;
}
export interface Hljs {
    highlight: HL<PERSON><PERSON><PERSON>['highlight'];
    getLanguage: H<PERSON>JSA<PERSON>['getLanguage'];
}
export default function useHljs(props: UseHljsProps, shouldHighlightRef?: Ref<boolean>): ComputedRef<Hljs | undefined>;
export {};
