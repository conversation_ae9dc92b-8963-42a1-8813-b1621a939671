import type { CSSProperties } from 'vue';
import type { CreateRowClassName, InternalRowData, RowData, SortOrder, SortOrderFlag, SortState, TableBaseColumn, TableColumn, TableExpandColumn, TableSelectionColumn } from './interface';
import type { DataTableGetCsvCell, DataTableGetCsvHeader } from './publicTypes';
export declare const SELECTION_COL_WIDTH = 40;
export declare const EXPAND_COL_WIDTH = 40;
export declare function getNumberColWidth(col: TableColumn): number | undefined;
export declare function getStringColWidth(col: TableColumn): string | undefined;
export declare function getColKey(col: TableColumn): string | number;
export declare function createShallowClonedObject<T>(object: T): T;
export declare function getFlagOfOrder(order: SortOrder): SortOrderFlag;
export declare function clampValueFollowCSSRules(value: number, min?: number | string, max?: number | string): number;
export declare function createCustomWidthStyle(column: TableBaseColumn | TableSelectionColumn | TableExpandColumn, resizedWidth?: string): CSSProperties;
export declare function createRowClassName(row: InternalRowData, index: number, rowClassName?: string | CreateRowClassName): string;
export declare function shouldUseArrayInSingleMode(column: TableBaseColumn): boolean;
export declare function isColumnSortable(column: TableColumn): boolean;
export declare function isColumnResizable(column: TableColumn): boolean;
export declare function isColumnFilterable(column: TableColumn): boolean;
export declare function createNextSorter(column: TableBaseColumn, currentSortState: SortState | null): SortState | null;
export declare function isColumnSorting(column: TableColumn, mergedSortState: SortState[]): boolean;
export declare function generateCsv(columns: TableColumn[], data: RowData[], getCsvCell: DataTableGetCsvCell | undefined, getCsvHeader: DataTableGetCsvHeader | undefined): string;
